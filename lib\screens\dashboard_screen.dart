import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'widgets/app_drawer.dart';
import 'widgets/common/dashboard_card.dart';

class DashboardScreen extends StatelessWidget {
  const DashboardScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Dashboard"),
        backgroundColor: Colors.blue,
      ),
      drawer: const AppDrawer(),
      body: Container(
        width: double.infinity,
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0xFF6dd5ed), Color(0xFF2193b0)],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: GridView.count(
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              DashboardCard(
                icon: Icons.person,
                title: "Profile",
                onTap: () => context.go('/profile'),
              ),
              DashboardCard(icon: Icons.message, title: "Messages"),
              DashboardCard(icon: Icons.notifications, title: "Notifications"),
              DashboardCard(
                icon: Icons.settings,
                title: "Settings",
                onTap: () => context.go('/settings'),
              ),
              DashboardCard(
                icon: Icons.analytics,
                title: "Reports",
                onTap: () => context.go('/reports'),
              ),
              DashboardCard(icon: Icons.help, title: "Help"),
            ],
          ),
        ),
      ),
    );
  }
}
