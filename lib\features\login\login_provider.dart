import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../core/api_client.dart';
import '../../core/storage_service.dart';

/// Provider ? Login StateNotifier
final loginProvider = StateNotifierProvider<LoginNotifier, bool>((ref) {
  return LoginNotifier(ref);
});

class LoginNotifier extends StateNotifier<bool> {
  final Ref ref;
  LoginNotifier(this.ref) : super(false);

  /// ?? Login method
  Future<void> login(
    String username,
    String password,
    BuildContext context,
  ) async {
    try {
      final response = await ref
          .read(apiClientProvider)
          .post(
            "/auth-service/auth/authenticate",
            data: {"username": username, "password": password},
          );

      final token = response.data["accessToken"];
      final officeCode = response.data["officeCode"];

      // Save to secure storage
      await ref.read(storageServiceProvider).saveAuth(token, officeCode);
      state = true;

      /// ? Navigate using GoRouter
      if (context.mounted) {
        context.go("/dashboard");
      }
    } catch (e) {
      rethrow;
    }
  }

  /// ?? Logout method
  Future<void> logout(BuildContext context) async {
    // 1. Storage clear
    await ref.read(storageServiceProvider).clearAuth();

    // 2. Provider state reset
    state = false;

    // 3. Navigate to login screen
    if (context.mounted) {
      context.go("/login");
    }
  }
}
