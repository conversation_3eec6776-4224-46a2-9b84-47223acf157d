class LoginResponse {
  final String accessToken;
  final String tokenType;
  final String officeCode;

  LoginResponse({
    required this.accessToken,
    required this.tokenType,
    required this.officeCode,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      accessToken: json['accessToken'],
      tokenType: json['tokenType'],
      officeCode: json['officeCode'],
    );
  }
}
