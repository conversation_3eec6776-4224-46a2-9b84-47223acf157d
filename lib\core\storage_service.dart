import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Provider for StorageService
final storageServiceProvider = Provider<StorageService>((ref) {
  return StorageService();
});

class StorageService {
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();

  static const String tokenKey = "authToken";
  static const String officeKey = "officeCode";
  static const String themeKey = "isDarkMode";

  // ---- Auth ----
  Future<void> saveAuth(String token, String officeCode) async {
    await _secureStorage.write(key: tokenKey, value: token);
    await _secureStorage.write(key: officeKey, value: officeCode);
  }

  Future<String?> getToken() async {
    return await _secureStorage.read(key: tokenKey);
  }

  Future<String?> getOfficeCode() async {
    return await _secureStorage.read(key: office<PERSON>ey);
  }

  Future<void> clearAuth() async {
    await _secureStorage.delete(key: tokenKey);
    await _secureStorage.delete(key: officeKey);
  }

  // ---- Theme ----
  Future<void> saveTheme(bool isDarkMode) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(themeKey, isDarkMode);
  }

  Future<bool> getTheme() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(themeKey) ?? false; // default light
  }
}
