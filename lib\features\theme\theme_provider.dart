import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/storage_service.dart';
import '../../core/app_theme.dart';

// Provider for current ThemeMode
final themeProvider = StateNotifierProvider<ThemeNotifier, ThemeData>(
  (ref) => ThemeNotifier(ref),
);

// ThemeNotifier handles theme switching
class ThemeNotifier extends StateNotifier<ThemeData> {
  final Ref ref;

  ThemeNotifier(this.ref) : super(AppTheme.lightTheme) {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    final isDark = await ref.read(storageServiceProvider).loadTheme();
    state = isDark ? AppTheme.darkTheme : AppTheme.lightTheme;
  }

  Future<void> toggleTheme() async {
    if (state == AppTheme.lightTheme) {
      state = AppTheme.darkTheme;
      await ref.read(storageServiceProvider).saveTheme(true);
    } else {
      state = AppTheme.lightTheme;
      await ref.read(storageServiceProvider).saveTheme(false);
    }
  }
}
