import 'package:flutter/material.dart';

import 'widgets/app_drawer.dart';

class ReportsScreen extends StatelessWidget {
  const ReportsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Reports"),
        backgroundColor: Colors.blue,
      ),
      drawer: const AppDrawer(),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: ListTile(
              leading: const Icon(Icons.insert_chart, color: Colors.blue),
              title: const Text("Sales Report"),
              subtitle: const Text("View monthly sales statistics"),
              onTap: () {},
            ),
          ),
          const SizedBox(height: 12),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: ListTile(
              leading: const Icon(Icons.bar_chart, color: Colors.green),
              title: const Text("Performance Report"),
              subtitle: const Text("Employee performance summary"),
              onTap: () {},
            ),
          ),
          const SizedBox(height: 12),
          Card(
            elevation: 4,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            child: ListTile(
              leading: const Icon(Icons.pie_chart, color: Colors.orange),
              title: const Text("Expense Report"),
              subtitle: const Text("Track expenses and cost analysis"),
              onTap: () {},
            ),
          ),
        ],
      ),
    );
  }
}
