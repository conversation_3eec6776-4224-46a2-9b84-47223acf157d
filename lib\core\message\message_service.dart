import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'message_widget.dart';

final messageProvider = StateProvider<String?>((ref) => null);

class MessageService {
  static void show(WidgetRef ref, BuildContext context, String message) {
    ref.read(messageProvider.notifier).state = message;

    final overlay = Overlay.of(context);
    late OverlayEntry entry;

    entry = OverlayEntry(
      builder: (_) => Consumer(
        builder: (context, ref, _) {
          final msg = ref.watch(messageProvider);
          if (msg == null) return const SizedBox.shrink();
          return MessageWidget(
            message: msg,
            onClose: () {
              ref.read(messageProvider.notifier).state = null;
              entry.remove();
            },
          );
        },
      ),
    );

    overlay.insert(entry);

    // auto dismiss after 3 sec
    Timer(const Duration(seconds: 3), () {
      if (ref.read(messageProvider) != null) {
        ref.read(messageProvider.notifier).state = null;
        if (entry.mounted) {
          entry.remove();
        }
      }
    });
  }
}
