import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../core/api_client.dart';
import 'login_model.dart';

// Repository provider
final loginRepositoryProvider = Provider<LoginRepository>((ref) {
  final apiClient = ref.read(apiClientProvider);
  return LoginRepository(apiClient);
});

class LoginRepository {
  final ApiClient client;

  LoginRepository(this.client);

  Future<LoginResponse> authenticate(String username, String password) async {
    final response = await client.post(
      "/auth-service/auth/authenticate",
      data: {"username": username, "password": password},
    );

    return LoginResponse.fromJson(response.data);
  }
}
