import 'package:flutter/material.dart';

import 'widgets/app_drawer.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text("Profile"),
        backgroundColor: Colors.blue,
      ),
      drawer: const AppDrawer(),
      body: Center(
        child: Card(
          margin: const EdgeInsets.all(20),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          elevation: 6,
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: const [
                CircleAvatar(
                  radius: 50,
                  backgroundColor: Colors.blue,
                  child: Icon(Icons.person, size: 60, color: Colors.white),
                ),
                Sized<PERSON><PERSON>(height: 20),
                Text(
                  "Kishan Manvar",
                  style: TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
                ),
                SizedB<PERSON>(height: 8),
                Text(
                  "<EMAIL>",
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                ),
                <PERSON><PERSON><PERSON><PERSON>(height: 20),
                ListTile(
                  leading: Icon(Icons.phone),
                  title: Text("+91 98765 43210"),
                ),
                ListTile(
                  leading: Icon(Icons.location_on),
                  title: Text("Rajkot, Gujarat"),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
