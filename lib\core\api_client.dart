import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Dio instance provider
final apiClientProvider = Provider<ApiClient>((ref) {
  return ApiClient();
});

class ApiClient {
  final Dio _dio = Dio(
    BaseOptions(
      baseUrl: "http://46.37.122.247:9502", // ✅ base URL અહીં મુકવું
      connectTimeout: const Duration(seconds: 10),
      receiveTimeout: const Duration(seconds: 10),
      headers: {"Content-Type": "application/json", "accept": "*/*"},
    ),
  );

  Future<Response> post(
    String path, {
    Map<String, dynamic>? data,
    Map<String, String>? headers,
  }) async {
    return await _dio.post(
      path,
      data: data,
      options: Options(headers: headers),
    );
  }

  Future<Response> get(
    String path, {
    Map<String, dynamic>? params,
    Map<String, String>? headers,
  }) async {
    return await _dio.get(
      path,
      queryParameters: params,
      options: Options(headers: headers),
    );
  }
}
